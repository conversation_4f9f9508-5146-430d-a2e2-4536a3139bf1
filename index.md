# C++ & Qt 高频面试知识点大全

## 第一部分：C++ 核心语言特性 (Core C++)

### 1. 基础语法与概念
1.  `#include <file.h>` 和 `#include "file.h"` 的区别？
2.  `const` 关键字的作用？（修饰变量、指针、函数、类成员函数）
3.  `static` 关键字的作用？（修饰全局变量、局部变量、函数、类成员变量、类成员函数）
4.  `volatile` 关键字的作用和使用场景？
5.  `extern` 关键字的作用？
6.  指针和引用的区别？
7.  C++中 `struct` 和 `class` 的区别？
8.  `new`/`delete` 和 `malloc`/`free` 的区别？
9.  `sizeof` 和 `strlen` 的区别？
10. 什么是内联函数（`inline`）？其优缺点是什么？
11. 描述C++的编译和链接过程。

### 2. 面向对象编程 (OOP)
12. 什么是面向对象？描述其三大特性（封装、继承、多态）。
13. 什么是封装？如何实现？
14. 什么是继承？C++中有哪些继承方式（`public`, `protected`, `private`）？它们的区别是什么？
15. 什么是多态？C++如何实现多态？（静态多态/编译时多态 vs 动态多态/运行时多态）
16. 什么是虚函数（`virtual function`）？其实现原理是什么（虚函数表 v-table）？
17. 什么是纯虚函数？包含纯虚函数的类称为什么？
18. 析构函数为什么通常要声明为虚函数？构造函数可以是虚函数吗？
19. 什么是抽象类？它与接口有什么异同？
20. 构造函数、析构函数、拷贝构造函数、赋值运算符的理解。
21. 什么是“C++ Big Three/Five” (三/五法则)？
22. 什么是菱形继承（The Diamond Problem）？如何解决？（虚继承 `virtual inheritance`）
23. 构造函数和析构函数的调用顺序是怎样的？
24. 什么是友元（`friend`）？它破坏了封装性吗？

### 3. C++ 标准模板库 (STL)
25. STL包含哪些核心组件？（容器、算法、迭代器、仿函数、适配器、分配器）
26. `std::vector` 的实现原理？其扩容机制是怎样的？
27. `std::vector` 和 `std::list` 的区别和适用场景？
28. `std::map` 和 `std::unordered_map` 的区别、实现原理和适用场景？
29. `std::set` 和 `std::unordered_set` 的区别？
30. 迭代器的种类有哪些？（输入、输出、前向、双向、随机访问）
31. STL中 `sort` 算法的实现？（通常是IntroSort，结合了快速排序、堆排序和插入排序）
32. 什么是迭代器失效？哪些操作会导致 `std::vector` 的迭代器失效？
33. 什么是仿函数（Functor）？
34. STL中的空间配置器（Allocator）是做什么的？

### 4. 现代 C++ (C++11/14/17/20)
35. C++11有哪些重要的新特性？
36. 什么是右值引用（Rvalue Reference）？它解决了什么问题？
37. 什么是移动语义（Move Semantics）和完美转发（Perfect Forwarding）？`std::move` 和 `std::forward` 的区别？
38. 智能指针有哪些？（`std::unique_ptr`, `std::shared_ptr`, `std::weak_ptr`）
39. `std::shared_ptr` 的实现原理？如何解决循环引用的问题？（使用 `std::weak_ptr`）
40. `std::unique_ptr` 和 `std::shared_ptr` 的主要区别？
41. 什么是 Lambda 表达式？它的捕获方式有哪些？
42. `auto` 和 `decltype` 的区别？
43. `nullptr` 相比 `NULL` 有什么优势？
44. 范围 `for` 循环 (Range-based for loop) 的用法。
45. `override` 和 `final` 关键字的作用？
46. 什么是 `constexpr`？它和 `const` 有什么区别？
47. `std::thread` 的用法？如何避免数据竞争？（`std::mutex`, `std::lock_guard`）
48. `std::atomic` 是什么？它如何保证原子性？
49. `std::condition_variable` 的作用和使用场景？
50. C++17 的 `std::optional`, `std::any`, `std::variant` 分别是什么？

---

## 第二部分：Qt 框架

### 5. Qt 核心机制
51. 什么是信号与槽（Signals & Slots）机制？它的优缺点是什么？
52. 信号与槽的实现原理是什么？（MOC - Meta-Object Compiler）
53. `Q_OBJECT` 宏的作用是什么？
54. 信号与槽的连接方式有几种？（`Qt::DirectConnection`, `Qt::QueuedConnection`, `Qt::BlockingQueuedConnection` 等）
55. 如何在不同线程中使用信号与槽？
56. Qt的元对象系统（Meta-Object System）提供了哪些能力？
57. Qt的属性系统（Property System）是什么？（`Q_PROPERTY`）
58. `QObject` 的父子关系（Parent-Child Relationship）是如何工作的？它如何简化内存管理？
59. 什么是Qt的事件循环（Event Loop）？`QApplication::exec()` 做了什么？
60. 如何自定义一个事件并发送它？
61. `event()` 和 `eventFilter()` 的区别和用法？
62. Qt中的隐式共享（Implicit Sharing）/写时复制（Copy-on-Write）是什么？哪些类使用了这个技术？

### 6. Qt GUI 编程
63. `QWidget` 的生命周期是怎样的？
64. 什么是Qt的布局（Layout）系统？常用的布局有哪些？
65. 如何创建一个自定义的 `QWidget`？需要重写哪些关键函数？（如 `paintEvent`, `mousePressEvent` 等）
66. `paintEvent` 和 `QPainter` 是如何工作的？
67. `update()` 和 `repaint()` 的区别？
68. Qt的坐标系统是怎样的？（物理坐标、逻辑坐标）
69. 什么是Qt的模型/视图（Model/View）架构？它的好处是什么？
70. `QAbstractItemModel` 的关键虚函数有哪些？（`rowCount`, `columnCount`, `data`, `headerData` 等）
71. `QListView`, `QTableView`, `QTreeView` 有什么区别？
72. 什么是委托（Delegate）？在M/V架构中起什么作用？
73. Qt中有哪些常用的对话框类？
74. `QMainWindow`, `QWidget`, `QDialog` 的区别？
75. 如何使用QSS（Qt Style Sheets）来美化界面？

### 7. Qt 高级主题
76. Qt的多线程有哪几种实现方式？（`QThread`, `QtConcurrent`）
77. `QThread` 的正确使用方法是什么？（为什么不推荐继承 `QThread` 并重写 `run`？）
78. `moveToThread` 的工作原理是什么？
79. Qt的网络编程模块（`QTcpSocket`, `QUdpSocket`, `QNetworkAccessManager`）如何使用？
80. 如何在Qt中进行文件I/O操作？（`QFile`, `QTextStream`, `QDataStream`）
81. Qt的插件机制是如何实现的？
82. 什么是 `QVariant`？它有什么用？
83. Qt的国际化（i18n）和本地化（l10n）是如何实现的？（`tr()`, `QTranslator`）
84. `QPointer` 和 `QSharedPointer` 与普通指针或C++智能指针的区别？
85. 如何在Qt中与数据库交互？

---

## 第三部分：计算机科学基础与工具

### 8. 数据结构与算法
86. 解释栈（Stack）和队列（Queue）的特点。
87. 解释哈希表（Hash Table）的实现原理，以及如何解决哈希冲突。
88. 解释二叉树、平衡二叉树（AVL Tree）、红黑树（Red-Black Tree）的概念。
89. 常见的排序算法有哪些？（冒泡、选择、插入、快速排序、归并排序、堆排序），并说明它们的时间复杂度。
90. 什么是稳定排序和不稳定排序？
91. 深度优先搜索（DFS）和广度优先搜索（BFS）的区别。

### 9. 设计模式
92. 什么是单例模式（Singleton Pattern）？如何实现一个线程安全的单例？
93. 什么是工厂模式（Factory Pattern）？（简单工厂、工厂方法、抽象工厂）
94. 什么是观察者模式（Observer Pattern）？（Qt的信号槽是其变体）
95. 什么是策略模式（Strategy Pattern）？
96. 什么是装饰器模式（Decorator Pattern）？

### 10. 开发与调试工具
97. 你常用的IDE是什么？（如 Qt Creator, Visual Studio, CLion）
98. 你如何使用CMake或qmake来管理你的项目？
99. 你常用的调试工具是什么？（GDB, LLDB, Visual Studio Debugger）
100. 你如何定位和解决内存泄漏问题？（Valgrind, ASan, Qt Creator的分析工具）
